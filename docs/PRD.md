# PRD — TriTimesheet (format: Markdown)

Version: 1.0  
Date: 2025-09-06  
Rédigé par: Ingénieur logiciel / Architecte web

---

## 1. Vision & objectifs

TriTimesheet est une application web de saisie et de validation de feuilles de temps destinée aux entreprises de consulting. Elle permet aux consultants de renseigner leurs heures par créneaux de 30 minutes, groupés en tâches décrites et affectées à un client, sur des semaines calendaires standardisées. Les administrateurs gèrent les consultants/clients et valident les timesheets.

Objectifs clés:
- Saisie rapide et fiable des heures, avec interactions fluides (sélection à la souris, drag & drop).
- Simplicité d’usage et UI sobre et moderne, couleurs reposantes.
- Internationalisation avec anglais par défaut (français activable).
- <PERSON><PERSON><PERSON><PERSON>, compatibilité multi-navigateurs, performances et sécurité élevées.
- Pile technique imposée: <PERSON><PERSON> (backend), <PERSON><PERSON> (frontend), MySQL (DB, latest).

---

## 2. Périmètre

- Inclus:
  - Authentification de consultants et d’administrateurs (comptes créés en amont).
  - Création/saisie/consultation des timesheets par consultant.
  - Calculs d’heures et règles (max 8h/jour, créneaux de 30 min).
  - Gestion des états des timesheets (brouillon, soumis, validé, rejeté).
  - Administration (CRUD) des clients et consultants.
  - Filtrage, tri (du plus récent au plus ancien), et validation côté admin.
  - I18n, UI réactive, drag & drop, sélection contiguë de slots.
- Exclusions (v1):
  - Facturation, projets détaillés, export avancé (CSV/PDF) — optionnelle en v1.1.
  - Intégration SSO/LDAP — optionnelle en v1.x.
  - Workflow de congés complet (demande/approbation) — hors périmètre; seule l’annotation demi-journée non comptabilisée est prévue.

---

## 3. Personas & rôles

- Consultant
  - Se connecte, crée un timesheet pour un lundi disponible.
  - Saisit des slots de 30 min, groupe en tâches, affecte un client, décrit la tâche.
  - Peut marquer des demi-journées comme congés/absence/holidays (non comptabilisées).
  - Consulte l’historique de ses timesheets.
  - Soumet le timesheet à validation.

- Administrateur
  - CRUD clients.
  - CRUD consultants.
  - Visualise, filtre, trie tous les timesheets.
  - Valide ou rejette les timesheets (avec commentaire de rejet).

---

## 4. Exigences fonctionnelles

### 4.1 Authentification & autorisations
- Les comptes consultants/admin sont pré-créés.
- Auth locale par email + mot de passe (hash bcrypt/argon2).
- RBAC simple: roles = [consultant, admin].
- Session JWT (HTTP-only cookie) ou session Laravel + CSRF.

### 4.2 Internationalisation
- Anglais par défaut. L’UI détecte la langue du navigateur et bascule si FR présent.
- Bibliothèque: ngx-translate avec fichiers i18n JSON (en, fr).
- Textes statiques et labels intégralement externalisés.

### 4.3 Timesheets — modèle & règles
- Un timesheet correspond à une semaine calendaire: lundi → samedi.
- Sélection du lundi uniquement dans un datepicker:
  - Seuls les lundis non déjà utilisés par le consultant sont sélectionnables.
  - Lundi obligatoire pour créer un timesheet.
- Samedi:
  - Désactivé par défaut (non saisissable).
  - Peut être activé manuellement au niveau du timesheet; une fois activé, devient saisissable.
- Jours et créneaux:
  - Jours affichés en colonnes (Lun → Sam), Sam potentiellement grisé.
  - Chaque jour découpé en slots de 30 minutes.
  - Maximum 8h par jour (soit 16 slots).
  - Demi-journées marquées comme congés/absences/holidays:
    - Choix AM (matin) ou PM (après-midi) par jour.
    - Les demi-journées marquées ne sont pas comptabilisées dans le total d’heures.
    - Les slots correspondants sont verrouillés à la saisie (ou exclus du total).
- Groupes de slots (tâches):
  - L’utilisateur sélectionne des slots contigus à la souris pour créer/grossir une tâche.
  - Chaque groupe doit avoir:
    - Client (si un seul client existe dans le système, il est sélectionné par défaut).
    - Description (nom de la tâche).
  - Si l’utilisateur sélectionne des slots sur un jour suivant sans avoir nommé le groupe précédent, ces slots sont rattachés à la même tâche (tâche multi-jours).
  - Drag & drop pour réorganiser/étendre des groupes, au sein d’un jour ou d’un autre jour de la même semaine.
- Calculs:
  - Affichage en temps réel du total d’heures saisies par jour et par semaine (excluant congés/absences/holidays).
  - Contrôle strict du plafond de 8h/jour (message d’erreur et blocage).
- États & workflow:
  - Brouillon: modifiable par le consultant.
  - Soumis: lecture seule pour consultant; visible pour admin pour validation.
  - Validé: verrouillé; visible par les deux.
  - Rejeté: redevient modifiable par le consultant; commentaire obligatoire côté admin.

### 4.4 Listes et filtres
- Consultant: liste de ses timesheets (tri du plus récent au plus ancien), filtres par:
  - Période (mois/année), statut, client présent dans la semaine.
- Admin: liste globale, mêmes filtres + consultant.
- Pagination (20 éléments/page par défaut).

### 4.5 Administration
- Clients (CRUD):
  - Attributs: nom, code externe (optionnel), actif/inactif.
  - Contrainte: nom unique.
- Consultants (CRUD):
  - Attributs: nom, prénom, email (unique), rôle, statut (actif/inactif), langue préférée.
  - Réinitialisation de mot de passe par admin.
- Historique des validations (audit) par timesheet.

### 4.6 UX/UI
- Design sobre, moderne, couleurs reposantes (palette neutre, verts/bleus doux).
- Sélection des slots à la souris (cliquer-glisser), prise en charge tactile.
- Drag & drop fluide (Angular CDK).
- Indicateurs clairs: états des slots (vide, sélectionné, en congé, verrouillé, dépassement).
- Feedbacks non-intrusifs (toasts/snackbars).
- Accessibilité: contrastes AA, navigation clavier, ARIA.
- Support de thèmes clair/sombre.

---

## 5. Exigences non-fonctionnelles

- Performance:
  - TTI < 2s sur réseau 4G moyen pour pages principales.
  - Rendu de la grille hebdo < 100 ms lors des interactions (virtualisation si nécessaire).
- Scalabilité: multi-entreprises sur un même serveur (prévoir isolation logique par “tenant_id” v1.1; v1 mono-tenant).
- Disponibilité: cible 99.9% (intra-entreprise).
- Sécurité:
  - OWASP Top 10: CSRF, XSS, SQLi, SSRF…
  - Mots de passe hash argon2id, politiques de complexité.
  - Journaux d’audit pour actions sensibles (CRUD admin, validation).
- Compatibilité navigateurs:
  - Chrome, Edge, Firefox, Safari, versions dernières 2.
- Observabilité:
  - Logging structuré (Monolog), traçabilité des erreurs, métriques de base (nombre de timesheets soumis/validés).
- Sauvegardes DB quotidiennes, rétention 30 jours.

---

## 6. Parcours utilisateurs (résumé)

1) Consultant crée un timesheet:
- Ouvre “Ajouter un timesheet”.
- Datepicker affiche uniquement les lundis; ceux déjà utilisés sont désactivés.
- Sélectionne un lundi → crée “Timesheet #YYYY-Www”.
- Samedi désactivé par défaut; toggle pour activer si besoin.

2) Saisie:
- Sélection par cliquer-glisser des slots de 30 min sur un jour → ouverture d’un panneau latéral:
  - Si un seul client existe: pré-sélectionné.
  - Saisir description; sinon si l’utilisateur continue à sélectionner sur jour suivant sans renseigner, les slots s’ajoutent au même groupe (même tâche).
- Drag & drop pour ajuster les tâches.
- Marquer AM/PM “congé/absence/holiday” sur un jour (boutons AM/PM).
- Totaux jour/semaine se mettent à jour; blocage si >8h/jour.

3) Soumission:
- Bouton “Soumettre” (avec résumé des heures).
- État passe à “Soumis” (read-only).

4) Validation admin:
- Liste triée du plus récent; filtres (statut, consultant, client, période).
- Détail timesheet → “Valider” ou “Rejeter” (commentaire requis si rejet).
- Journalisation de l’action.

5) Consultation:
- Consultant voit ses timesheets (ordre du plus récent au plus ancien).
- Admin voit l’ensemble, mêmes filtres.

---

## 7. Données & modèle

Entités principales (simplifiées):

- users
  - id (PK), first_name, last_name, email (unique), password_hash, role [consultant|admin], locale [en|fr], active (bool), created_at, updated_at

- clients
  - id (PK), name (unique), external_code (nullable), active (bool), created_at, updated_at

- timesheets
  - id (PK), user_id (FK users), week_monday_date (DATE, unique par user_id), saturday_enabled (bool, default false), status [draft|submitted|approved|rejected], admin_comment (nullable), created_at, updated_at

- timesheet_days
  - id (PK), timesheet_id (FK), date (DATE, lun→sam), halfday_off_am (bool), halfday_off_pm (bool)
  - computed fields (matérialisés optionnels): day_minutes_total (int, excluant offs)

- tasks
  - id (PK), timesheet_id (FK), client_id (FK), description (TEXT), color (nullable), created_at, updated_at

- task_slots
  - id (PK), task_id (FK), day_date (DATE), start_time (TIME, aligné 00 ou 30), end_time (TIME, multiple de 30), UNIQUE(task_id, day_date, start_time)
  - Contrainte: durée multiple de 30 min

- audits
  - id (PK), actor_user_id, entity_type, entity_id, action, metadata (JSON), created_at

Contraintes/validations serveur:
- UNIQUE (user_id, week_monday_date) sur timesheets.
- Pour chaque jour:
  - Somme des durées des task_slots ≤ 8h − durée des demi-journées off (si on souhaite bloquer la saisie sur ces créneaux) OU au minimum exclues du total si autorisé de chevaucher visuellement; recommandé: slots interdits sur périodes AM/PM off.
- task_slots ne peuvent pas se chevaucher pour une même date et même task; et idéalement, pas de chevauchement inter-task sur une même date (contrainte logique par validation applicative).

---

## 8. API (REST) — endpoints principaux

Base: /api/v1

Auth:
- POST /auth/login
- POST /auth/logout
- GET /auth/me

Consultants:
- GET /timesheets?mine=true|false&status=&from=&to=&clientId=&page=
- POST /timesheets { weekMondayDate, saturdayEnabled? }
- GET /timesheets/{id}
- PATCH /timesheets/{id} { saturdayEnabled?, status? [draft|submitted] } — soumission via status=submitted
- GET /timesheets/{id}/summary
- POST /timesheets/{id}/days/{date}/halfday { am?:bool, pm?:bool, reason: [holiday|absence] }
- POST /timesheets/{id}/tasks { clientId, description }
- PATCH /timesheets/{id}/tasks/{taskId} { clientId?, description? }
- DELETE /timesheets/{id}/tasks/{taskId}
- POST /timesheets/{id}/tasks/{taskId}/slots { dayDate, startTime, endTime }
- PATCH /timesheets/{id}/tasks/{taskId}/slots/{slotId} { dayDate?, startTime?, endTime? }
- DELETE /timesheets/{id}/tasks/{taskId}/slots/{slotId}

Admin:
- GET /admin/timesheets?consultantId=&status=&from=&to=&clientId=&page=
- PATCH /admin/timesheets/{id}/status { status: approved|rejected, comment? }
- CRUD Clients:
  - GET /admin/clients
  - POST /admin/clients
  - PATCH /admin/clients/{id}
  - DELETE /admin/clients/{id}
- CRUD Consultants:
  - GET /admin/users?role=consultant
  - POST /admin/users
  - PATCH /admin/users/{id}
  - DELETE /admin/users/{id}

Erreurs standardisées (JSON):
- 400 (validation_error), 401 (unauthorized), 403 (forbidden), 404 (not_found), 409 (conflict e.g., lundi déjà utilisé), 422 (rule_violation), 500.

---

## 9. Règles métier (détails)

- Lundi disponible:
  - Est un lundi (date % 7 selon locale ISO-8601).
  - Aucun timesheet existant pour l’utilisateur avec ce week_monday_date.
- Samedi:
  - Valeur par défaut: désactivé.
  - Si activé, il devient un jour comme les autres, soumis au plafond 8h.
- Demi-journées off:
  - AM: 08:00–12:00; PM: 13:00–17:00 (plages par défaut, paramétrables v1.1).
  - Les slots ne peuvent pas être créés sur une demi-journée off (validation UI + API).
  - Heures off non comptabilisées dans le total.
- Groupes multi-jours sans nom:
  - Tant que “description” non renseignée, toute sélection contiguë sur jours suivants s’ajoute au même groupe temporaire.
  - À la première saisie de description, le groupe est “fixé”.
- Calcul total:
  - Total semaine = somme des slots valides (excluant périodes off) sur Lun→Sam activés.
  - Totaux par jour affichés; avertissement si approche de 8h; blocage au-delà.
- États:
  - Brouillon → Soumis: nécessite au moins un slot valide (ou zéro? Décision: ≥ 1 slot).
  - Soumis → Validé/Rejeté: admin uniquement; commentaire requis si rejet.
  - Validé/Rejeté → Brouillon: impossible sauf rejet qui remet modifiable.

---

## 10. UX — Composants & interactions

- Datepicker (Angular Material):
  - Filtre de dates pour n’autoriser que les lundis.
  - Désactiver lundis déjà pris (appel API de disponibilité).
- Grille hebdomadaire:
  - 6 colonnes (Lun…Sam), Sam grisé et inactif si disabled.
  - Lignes de 30 min (config 08:00–18:00 par défaut; paramétrable v1.1).
  - Sélection par cliquer-glisser crée un “draft task” → panneau latéral (client, description).
  - Drag & drop pour étendre/réduire/déplacer des blocs.
  - Indicateurs:
    - Bordures/ombrages par tâche, couleurs douces.
    - Hachures pour demi-journées off.
    - Tooltips sur survol (client, desc, durée).
- Résumés:
  - Totaux par jour en bas de colonne.
  - Total semaine, nombre de tâches, répartition par client.
- Listes:
  - Cartes/ligne par timesheet: Semaine (YYYY-Www), statut, total h, clients impliqués.
  - Tri par défaut: plus récents → plus anciens.
  - Filtres persistants (URL query).

Accessibilité:
- Focus visible, rôles ARIA, annonces live pour toasts, navigation clavier des slots.

---

## 11. Technologies & librairies

- Frontend: Angular 17+, TypeScript, Angular Material + CDK (DragDrop), ngx-translate, date-fns (gestion dates ISO-8601), RxJS.
- Backend: Laravel 11+, PHP 8.3+, Sanctum/Fortify pour auth, FormRequests pour validations, Eloquent.
- DB: MySQL 8.4+ (ou “latest” supportée), InnoDB, indexes composés, contraintes FK.
- Build/CI: Node 20+, Vite pour Angular, PHPStan/Psalm, PHP-CS-Fixer, ESLint/Prettier.
- Tests: PHPUnit + Pest, Laravel Dusk (e2e backend), Playwright/Cypress (e2e frontend).
- Conteneurisation: Docker Compose (app, db, nginx).
- Observabilité: Laravel Telescope (admin only), Sentry (FE/BE) optionnel.

---

## 12. Sécurité

- CSP stricte, cookies HTTP-only, SameSite=Lax/Strict, CSRF tokens.
- Rate limiting login.
- Politique de mot de passe, rotation possible via admin.
- Journaux d’audit pour CRUD admin et transitions d’état des timesheets.
- Masquage d’emails dans logs, RGPD: droit d’accès/suppression (admin).

---

## 13. Critères d’acceptation (échantillon)

- Création timesheet:
  - Étant connecté en tant que consultant, quand j’ouvre “Ajouter un timesheet”, alors seuls les lundis sont actifs dans le datepicker et les lundis déjà utilisés sont désactivés.
  - Quand je sélectionne un lundi valide, alors un timesheet en statut “brouillon” est créé et Samedi est désactivé par défaut.

- Saisie slots:
  - Quand je sélectionne des slots contigus sur mardi puis mercredi sans renseigner la description, alors ils appartiennent au même groupe temporaire.
  - Quand je saisis la description, alors le groupe est figé et de nouvelles sélections créent un nouveau groupe.

- Règle 8h/jour:
  - Si le total d’un jour atteint 8h, alors la création de nouveaux slots ce jour est bloquée avec un message d’erreur.

- Demi-journées off:
  - Quand je marque AM de jeudi en “holiday”, alors les slots 08:00–12:00 deviennent non saisissables et ne comptent pas dans le total.

- Client par défaut:
  - Si un seul client existe dans le système, alors ce client est pré-sélectionné lors de la création d’une tâche.

- Soumission/validation:
  - Quand je soumets le timesheet, alors il passe en “soumis” et devient en lecture seule pour moi.
  - Côté admin, quand je rejette un timesheet, un commentaire est obligatoire et le consultant peut à nouveau l’éditer.

- Listes:
  - Les listes de timesheets sont triées du plus récent au plus ancien par défaut; les filtres modifient l’affichage sans rechargement complet.

---

## 14. Maquettes textuelles (wireframes low-fi)

- Page “Mes timesheets”
  - [Bouton] Ajouter un timesheet
  - [Filtres] Période | Statut | Client
  - [Liste] Carte: Semaine YYYY-Www | Statut | Total h | Clients | [Voir]

- Page “Saisie timesheet”
  - Header: Semaine YYYY-Www | Statut: Brouillon | [Toggle Samedi]
  - Grille: colonnes Lun–Sam, lignes 30 min
  - Sidebar tâche (à l’édition):
    - Client: [Select]
    - Description: [Textarea]
    - Durée calculée
    - [Supprimer la tâche]
  - Footer: Totaux par jour | Total semaine | [Soumettre]

- Page “Admin timesheets”
  - [Filtres] Consultant | Statut | Période | Client
  - [Tableau] Consultant | Semaine | Total h | Statut | [Valider] [Rejeter]

- Admin “Clients/Consultants”
  - Tables CRUD basiques + recherche + pagination.

---

## 15. Plan de livraison

- Sprint 1: Auth, structures DB, CRUD admin (clients, consultants).
- Sprint 2: Création timesheet, datepicker (lundi only, disponibilité).
- Sprint 3: Grille hebdo, sélection slots, tâches, client par défaut.
- Sprint 4: Demi-journées off, limites 8h, totaux, samedi toggle.
- Sprint 5: Soumission/validation, listes, filtres, tri, audit.
- Sprint 6: I18n complet, accessibilité, tests e2e, optimisations perf.
- Sprint 7: Dploy Docker, sauvegardes, observabilité, durcissement sécurité.

---

## 16. Suivi qualité & métriques

- Taux d’erreurs API (<0.1% requêtes 5xx).
- Temps médian de saisie d’un timesheet complet (<5 min à terme).
- Taux de rejets admin (<10% après 1 mois).
- Couverture tests: BE ≥80%, FE ≥70%.

---

## 17. Risques & mitigations

- Complexité UI de sélection/drag & drop: s’appuyer sur Angular CDK + protos UX, tests utilisateurs internes.
- Chevauchement tâches/offs: validations côté UI + API, messages clairs.
- Fuseaux horaires: normaliser en date locale de l’entreprise; stocker heures en HH:MM sans TZ.

---

## 18. Notes d’implémentation

- Calcul “plus récent”:
  - Ordre par week_monday_date DESC puis created_at DESC.
- ISO semaine:
  - Génération clé “YYYY-Www” via date-fns (ISO-8601).
- Transactions:
  - Création/édition tâches + slots dans transactions DB pour éviter états partiels.
- Indexation:
  - Index composites: timesheets(user_id, week_monday_date), task_slots(task_id, day_date, start_time).

---

## 19. Glossaire

- Slot: créneau de 30 minutes.
- Tâche: groupe de slots avec client + description, potentiellement multi-jours.
- Demi-journée off: AM (08–12) ou PM (13–17) non comptabilisée.
- Semaine: Lundi → Samedi (Samedi désactivé par défaut, activable).
- Statuts timesheet: brouillon, soumis, validé, rejeté.

---

Ce PRD définit le comportement attendu de TriTimesheet, ses contraintes et interfaces. Il sert de référence pour la conception détaillée, le développement et les tests d’acceptation.
