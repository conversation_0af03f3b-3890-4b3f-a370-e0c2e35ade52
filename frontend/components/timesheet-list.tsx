"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Clock, Building, Eye } from "lucide-react"

interface Timesheet {
  id: number
  weekStart: string
  status: "draft" | "submitted" | "approved" | "rejected"
  totalHours: number
  clients: string[]
  lastModified: string
}

interface TimesheetListProps {
  timesheets: Timesheet[]
  onViewTimesheet: (timesheet: Timesheet) => void
}

export function TimesheetList({ timesheets, onViewTimesheet }: TimesheetListProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-muted text-muted-foreground"
      case "submitted":
        return "bg-accent text-accent-foreground"
      case "approved":
        return "bg-primary/10 text-primary"
      case "rejected":
        return "bg-destructive/10 text-destructive"
      default:
        return "bg-muted text-muted-foreground"
    }
  }

  const formatWeekRange = (weekStart: string) => {
    const start = new Date(weekStart)
    const end = new Date(start)
    end.setDate(start.getDate() + 5) // Friday

    return `${start.toLocaleDateString("en-US", { month: "short", day: "numeric" })} - ${end.toLocaleDateString("en-US", { month: "short", day: "numeric" })}`
  }

  return (
    <div className="space-y-4">
      {timesheets.map((timesheet) => (
        <Card key={timesheet.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-start gap-4">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Calendar className="h-5 w-5 text-primary" />
                </div>

                <div className="space-y-1">
                  <h3 className="font-semibold text-card-foreground">
                    Week of{" "}
                    {new Date(timesheet.weekStart).toLocaleDateString("en-US", {
                      month: "long",
                      day: "numeric",
                      year: "numeric",
                    })}
                  </h3>
                  <p className="text-sm text-muted-foreground">{formatWeekRange(timesheet.weekStart)}</p>

                  <div className="flex items-center gap-4 mt-2">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {timesheet.totalHours}h total
                    </div>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Building className="h-3 w-3" />
                      {timesheet.clients.join(", ")}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Badge className={getStatusColor(timesheet.status)}>
                  {timesheet.status.charAt(0).toUpperCase() + timesheet.status.slice(1)}
                </Badge>
                <Button variant="outline" size="sm" onClick={() => onViewTimesheet(timesheet)}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
