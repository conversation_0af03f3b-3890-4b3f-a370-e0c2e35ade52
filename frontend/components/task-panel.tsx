"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { X, Clock, Building } from "lucide-react"

interface Task {
  id: string
  clientId: string
  clientName: string
  description: string
  color: string
  slots: string[]
}

interface TaskPanelProps {
  task: Task
  onClose: () => void
  onSave: (task: Task) => void
  onDelete?: (taskId: string) => void
  isEditing?: boolean
}

export function TaskPanel({ task, onClose, onSave, onDelete, isEditing = false }: TaskPanelProps) {
  const [formData, setFormData] = useState(task)

  const clients = [
    { id: "client-1", name: "Acme Corp", type: "client" },
    { id: "client-2", name: "TechStart", type: "client" },
    { id: "client-3", name: "Global Inc", type: "client" },
    { id: "holiday", name: "Holiday", type: "absence" },
    { id: "vacation", name: "Vacation", type: "absence" },
    { id: "absence", name: "Absence", type: "absence" },
  ]

  const colors = ["bg-chart-1", "bg-chart-2", "bg-chart-3", "bg-chart-4", "bg-chart-5"]

  useEffect(() => {
    // Auto-select client if only one exists
    if (clients.length === 1 && !formData.clientId) {
      setFormData((prev) => ({
        ...prev,
        clientId: clients[0].id,
        clientName: clients[0].name,
      }))
    }
  }, [])

  const handleClientChange = (clientId: string) => {
    const client = clients.find((c) => c.id === clientId)
    setFormData((prev) => ({
      ...prev,
      clientId,
      clientName: client?.name || "",
    }))
  }

  const handleSave = () => {
    if (!formData.clientId || !formData.description.trim()) {
      return // Basic validation
    }
    onSave(formData)
  }

  const handleDelete = () => {
    if (onDelete && task.id !== "new-task") {
      onDelete(task.id)
      onClose()
    }
  }

  const isAbsenceType = ["holiday", "vacation", "absence"].includes(formData.clientId)
  const totalHours = isAbsenceType ? 0 : task.slots.length * 0.5

  return (
    <Card className="w-80 h-fit">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{task.id === "new-task" ? "New Task" : "Edit Task"}</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        {isEditing && task.id !== "new-task" && (
          <div className="text-xs text-muted-foreground bg-accent/20 px-2 py-1 rounded">
            Editing mode: Right-click slots to remove, left-click empty slots to add
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Time Summary */}
        <div className="flex items-center gap-2 p-3 bg-muted/30 rounded-lg">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">{isAbsenceType ? "Not counted" : `${totalHours}h selected`}</span>
          <Badge variant="outline" className="ml-auto text-xs">
            {task.slots.length} slots
          </Badge>
        </div>

        {/* Client Selection */}
        <div className="space-y-2">
          <Label htmlFor="client">Client</Label>
          <Select value={formData.clientId} onValueChange={handleClientChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a client" />
            </SelectTrigger>
            <SelectContent>
              {clients.map((client) => (
                <SelectItem key={client.id} value={client.id}>
                  <div className="flex items-center gap-2">
                    {client.type === "absence" ? (
                      <div className="h-4 w-4 rounded-full bg-orange-500" />
                    ) : (
                      <Building className="h-4 w-4" />
                    )}
                    <span className={client.type === "absence" ? "text-orange-600 font-medium" : ""}>
                      {client.name}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Task Description */}
        <div className="space-y-2">
          <Label htmlFor="description">Task Description</Label>
          <Textarea
            id="description"
            placeholder="Describe what you worked on..."
            value={formData.description}
            onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
            rows={3}
          />
        </div>

        {/* Color Selection */}
        <div className="space-y-2">
          <Label>Color</Label>
          <div className="flex gap-2">
            {colors.map((color) => (
              <button
                key={color}
                className={`w-6 h-6 rounded-full border-2 ${color} ${
                  formData.color === color ? "border-primary" : "border-border"
                }`}
                onClick={() => setFormData((prev) => ({ ...prev, color }))}
              />
            ))}
          </div>
        </div>

        {/* Slot Details */}
        <div className="space-y-2">
          <Label>Time Slots</Label>
          <div className="max-h-32 overflow-y-auto space-y-1">
            {task.slots.map((slot) => {
              const [day, time] = slot.split("-")
              return (
                <div key={slot} className="flex items-center justify-between text-sm p-2 bg-muted/20 rounded">
                  <span className="capitalize">{day}</span>
                  <span className="font-mono">{time}</span>
                </div>
              )
            })}
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button variant="outline" onClick={onClose} className="flex-1 bg-transparent">
            Cancel
          </Button>
          <Button onClick={handleSave} className="flex-1" disabled={!formData.clientId || !formData.description.trim()}>
            {task.id === "new-task" ? "Create Task" : "Update Task"}
          </Button>
        </div>

        {task.id !== "new-task" && (
          <Button variant="destructive" size="sm" className="w-full" onClick={handleDelete}>
            Delete Task
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
