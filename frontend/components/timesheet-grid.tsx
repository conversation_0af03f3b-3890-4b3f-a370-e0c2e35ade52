"use client"

import type React from "react"

import { useState, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Clock, Coffee, Calendar, AlertCircle, CheckCircle } from "lucide-react"
import { TaskPanel } from "./task-panel"
import { SubmissionDialog } from "./submission-dialog"

interface TimeSlot {
  id: string
  time: string
  taskId?: string
  isSelected?: boolean
  isHalfDayOff?: boolean
}

interface Task {
  id: string
  clientId: string
  clientName: string
  description: string
  color: string
  slots: string[]
}

export function TimesheetGrid() {
  const [selectedSlots, setSelectedSlots] = useState<string[]>([])
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: "task-1",
      clientId: "client-1",
      clientName: "Acme Corp",
      description: "Frontend Development",
      color: "bg-chart-1",
      slots: ["mon-09:00", "mon-09:30", "mon-10:00", "tue-09:00", "tue-09:30"],
    },
    {
      id: "task-2",
      clientId: "client-2",
      clientName: "TechStart",
      description: "API Integration",
      color: "bg-chart-2",
      slots: ["wed-14:00", "wed-14:30", "wed-15:00", "thu-14:00"],
    },
  ])
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [isSelecting, setIsSelecting] = useState(false)
  const [isEditingTask, setIsEditingTask] = useState(false)
  const [editingTaskId, setEditingTaskId] = useState<string | null>(null)
  const [multiSelectSlots, setMultiSelectSlots] = useState<string[]>([])
  const [isMultiSelecting, setIsMultiSelecting] = useState(false)
  const gridRef = useRef<HTMLDivElement>(null)

  const [timesheetStatus, setTimesheetStatus] = useState<"draft" | "submitted" | "approved" | "rejected">("draft")
  const [showSubmissionDialog, setShowSubmissionDialog] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [rejectionReason, setRejectionReason] = useState("")

  const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
  const dayAbbr = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
  const timeSlots = []

  for (let hour = 7; hour < 19; hour++) {
    timeSlots.push(`${hour.toString().padStart(2, "0")}:00`)
    timeSlots.push(`${hour.toString().padStart(2, "0")}:30`)
  }

  const getSlotId = (day: string, time: string) => `${day.toLowerCase().slice(0, 3)}-${time}`

  const getTaskForSlot = (slotId: string) => {
    return tasks.find((task) => task.slots.includes(slotId))
  }

  const handleSlotClick = (slotId: string) => {
    console.log("[v0] Slot clicked:", slotId)
    const task = getTaskForSlot(slotId)
    console.log("[v0] Task found for slot:", task)

    if (isEditingTask && editingTaskId) {
      const currentTask = tasks.find((t) => t.id === editingTaskId)
      if (currentTask && !task) {
        const updatedTasks = tasks.map((t) => (t.id === editingTaskId ? { ...t, slots: [...t.slots, slotId] } : t))
        setTasks(updatedTasks)
        const updatedTask = updatedTasks.find((t) => t.id === editingTaskId)
        if (updatedTask) {
          setSelectedTask(updatedTask)
        }
        console.log("[v0] Added slot to editing task:", slotId)
      }
    } else if (task) {
      console.log("[v0] Opening task panel for task:", task.id)
      setSelectedTask(task)
      setIsEditingTask(true)
      setEditingTaskId(task.id)
    } else {
      if (selectedSlots.includes(slotId)) {
        setSelectedSlots(selectedSlots.filter((id) => id !== slotId))
      } else {
        setSelectedSlots([...selectedSlots, slotId])
      }
    }
  }

  const handleMouseDown = (slotId: string) => {
    if (isEditingTask && editingTaskId) {
      const task = getTaskForSlot(slotId)
      if (!task) {
        setIsMultiSelecting(true)
        setMultiSelectSlots([slotId])
        console.log("[v0] Starting multi-select for editing task:", slotId)
      }
    } else {
      setIsSelecting(true)
      handleSlotClick(slotId)
    }
  }

  const handleMouseEnter = (slotId: string) => {
    if (isMultiSelecting && isEditingTask && editingTaskId) {
      const task = getTaskForSlot(slotId)
      if (!task && !multiSelectSlots.includes(slotId)) {
        setMultiSelectSlots([...multiSelectSlots, slotId])
        console.log("[v0] Added to multi-select:", slotId)
      }
    } else if (isSelecting && !getTaskForSlot(slotId)) {
      if (!selectedSlots.includes(slotId)) {
        setSelectedSlots([...selectedSlots, slotId])
      }
    }
  }

  const handleMouseUp = () => {
    if (isMultiSelecting && isEditingTask && editingTaskId && multiSelectSlots.length > 0) {
      const currentTask = tasks.find((t) => t.id === editingTaskId)
      if (currentTask) {
        const updatedTasks = tasks.map((t) =>
          t.id === editingTaskId ? { ...t, slots: [...t.slots, ...multiSelectSlots] } : t,
        )
        setTasks(updatedTasks)
        const updatedTask = updatedTasks.find((t) => t.id === editingTaskId)
        if (updatedTask) {
          setSelectedTask(updatedTask)
        }
        console.log("[v0] Added multiple slots to editing task:", multiSelectSlots)
      }
      setMultiSelectSlots([])
      setIsMultiSelecting(false)
    } else {
      setIsSelecting(false)
      if (selectedSlots.length > 0) {
        setSelectedTask({
          id: "new-task",
          clientId: "",
          clientName: "",
          description: "",
          color: "bg-accent",
          slots: selectedSlots,
        })
      }
    }
  }

  const getDayTotal = (day: string) => {
    const daySlots = tasks.flatMap((task) =>
      task.slots.filter((slot) => slot.startsWith(day.toLowerCase().slice(0, 3))),
    )
    return daySlots.length * 0.5 // Each slot is 30 minutes = 0.5 hours
  }

  const weekTotal = days.reduce((total, day) => total + getDayTotal(day), 0)

  const validateTimesheet = () => {
    const errors: string[] = []

    if (tasks.length === 0) {
      errors.push("At least one task must be added to submit timesheet")
    }

    const incompleteTasks = tasks.filter((task) => !task.clientName || !task.description)
    if (incompleteTasks.length > 0) {
      errors.push("All tasks must have a client and description")
    }

    if (weekTotal < 8) {
      errors.push("Minimum 8 hours required for submission")
    }

    const dailyOverages = days.filter((day) => getDayTotal(day) > 8)
    if (dailyOverages.length > 0) {
      errors.push(`Daily hour limit exceeded for: ${dailyOverages.join(", ")}`)
    }

    setValidationErrors(errors)
    return errors.length === 0
  }

  const handleSubmit = () => {
    if (validateTimesheet()) {
      setShowSubmissionDialog(true)
    }
  }

  const confirmSubmission = () => {
    setTimesheetStatus("submitted")
    setShowSubmissionDialog(false)
  }

  const handleSaveDraft = () => {
    const hasContent = tasks.length > 0 || weekTotal > 0
    if (hasContent) {
      console.log("[v0] Draft saved successfully")
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-muted text-muted-foreground"
      case "submitted":
        return "bg-accent text-accent-foreground"
      case "approved":
        return "bg-primary/10 text-primary"
      case "rejected":
        return "bg-destructive/10 text-destructive"
      default:
        return "bg-muted text-muted-foreground"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-4 w-4" />
      case "rejected":
        return <AlertCircle className="h-4 w-4" />
      default:
        return null
    }
  }

  const handleDeleteTask = (taskId: string) => {
    console.log("[v0] Deleting task:", taskId)
    setTasks(tasks.filter((task) => task.id !== taskId))
    setSelectedTask(null)
    setIsEditingTask(false)
    setEditingTaskId(null)
  }

  const handleSlotRightClick = (e: React.MouseEvent, slotId: string) => {
    e.preventDefault()

    if (isEditingTask && editingTaskId) {
      const task = getTaskForSlot(slotId)
      if (task && task.id === editingTaskId) {
        const updatedTasks = tasks.map((t) =>
          t.id === editingTaskId ? { ...t, slots: t.slots.filter((slot) => slot !== slotId) } : t,
        )
        setTasks(updatedTasks)
        const updatedTask = updatedTasks.find((t) => t.id === editingTaskId)
        if (updatedTask) {
          setSelectedTask(updatedTask)
        }
        console.log("[v0] Removed slot from editing task:", slotId)
      }
    }
  }

  const handleTaskSave = (updatedTask: Task) => {
    if (updatedTask.id === "new-task") {
      const newTask = {
        ...updatedTask,
        id: `task-${Date.now()}`,
        slots: selectedSlots,
      }
      setTasks([...tasks, newTask])
    } else {
      const taskToUpdate = selectedTask || updatedTask
      setTasks(tasks.map((t) => (t.id === updatedTask.id ? { ...updatedTask, slots: taskToUpdate.slots } : t)))
    }
    setSelectedTask(null)
    setSelectedSlots([])
    setIsEditingTask(false)
    setEditingTaskId(null)
  }

  const handleTaskPanelClose = () => {
    setSelectedTask(null)
    setSelectedSlots([])
    setIsEditingTask(false)
    setEditingTaskId(null)
    setMultiSelectSlots([])
    setIsMultiSelecting(false)
  }

  return (
    <div className="flex gap-6 h-full" onContextMenu={(e) => e.preventDefault()}>
      <div className="flex-1">
        {timesheetStatus !== "draft" && (
          <Card className="mb-4">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(timesheetStatus)}
                  <div>
                    <Badge className={getStatusColor(timesheetStatus)}>
                      {timesheetStatus.charAt(0).toUpperCase() + timesheetStatus.slice(1)}
                    </Badge>
                    <p className="text-sm text-muted-foreground mt-1">
                      {timesheetStatus === "submitted" && "Timesheet submitted for approval"}
                      {timesheetStatus === "approved" && "Timesheet approved by administrator"}
                      {timesheetStatus === "rejected" && `Timesheet rejected: ${rejectionReason}`}
                    </p>
                  </div>
                </div>
                {timesheetStatus === "rejected" && (
                  <Button variant="outline" size="sm" onClick={() => setTimesheetStatus("draft")}>
                    Edit & Resubmit
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        <Card className="h-full">
          <CardContent className="p-0">
            <div
              ref={gridRef}
              className="grid grid-cols-7 h-full"
              onMouseUp={handleMouseUp}
              onMouseLeave={() => {
                setIsSelecting(false)
                setIsMultiSelecting(false)
                setMultiSelectSlots([])
              }}
              onContextMenu={(e) => e.preventDefault()}
            >
              <div className="col-span-1 p-4 border-r border-b border-border bg-muted/30">
                <div className="text-sm font-medium text-muted-foreground">Time</div>
              </div>
              {days.map((day, index) => (
                <div key={day} className="p-4 border-r border-b border-border bg-muted/30">
                  <div className="text-sm font-medium text-foreground">{day}</div>
                  <div className="text-xs text-muted-foreground mt-1">Jan {8 + index}</div>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="text-xs">
                      {getDayTotal(day)}h
                    </Badge>
                    {index === 5 && (
                      <Badge variant="secondary" className="text-xs">
                        Optional
                      </Badge>
                    )}
                  </div>
                </div>
              ))}

              {timeSlots.map((time) => (
                <div key={time} className="contents">
                  <div className="p-2 border-r border-b border-border bg-muted/10 flex items-center">
                    <span className="text-xs text-muted-foreground font-mono">{time}</span>
                  </div>

                  {days.map((day, dayIndex) => {
                    const slotId = getSlotId(day, time)
                    const task = getTaskForSlot(slotId)
                    const isSelected = selectedSlots.includes(slotId)
                    const isHalfDayOff = false
                    const isEditingThisTask = isEditingTask && task?.id === editingTaskId
                    const isMultiSelected = multiSelectSlots.includes(slotId)

                    return (
                      <div
                        key={slotId}
                        className={`
                          border-r border-b border-border min-h-[40px] cursor-pointer transition-colors relative
                          ${task ? `${task.color} hover:opacity-80` : "hover:bg-accent/20"}
                          ${isSelected ? "bg-accent border-primary" : ""}
                          ${isMultiSelected ? "bg-primary/20 border-primary border-2" : ""}
                          ${isHalfDayOff ? "bg-muted/50 cursor-not-allowed" : ""}
                          ${dayIndex === 5 ? "bg-muted/20" : ""}
                          ${isEditingThisTask ? "ring-2 ring-primary ring-inset" : ""}
                        `}
                        onMouseDown={() => !isHalfDayOff && handleMouseDown(slotId)}
                        onMouseEnter={() => !isHalfDayOff && handleMouseEnter(slotId)}
                        onClick={() => !isHalfDayOff && handleSlotClick(slotId)}
                        onContextMenu={(e) => handleSlotRightClick(e, slotId)}
                        data-slot-id={slotId}
                        data-has-task={!!task}
                      >
                        {task && (
                          <div className="p-1 text-xs">
                            <div className="font-medium truncate">{task.clientName}</div>
                            <div className="text-xs opacity-75 truncate">{task.description}</div>
                          </div>
                        )}
                        {isHalfDayOff && (
                          <div className="absolute inset-0 flex items-center justify-center">
                            <Coffee className="h-3 w-3 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="mt-4">
          <CardContent className="p-4">
            {validationErrors.length > 0 && (
              <div className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-destructive mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-destructive">Validation Errors:</p>
                    <ul className="text-sm text-destructive/80 mt-1 space-y-1">
                      {validationErrors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Week Total: {weekTotal}h</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">{tasks.length} tasks</span>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSaveDraft}
                  disabled={timesheetStatus === "submitted" || timesheetStatus === "approved"}
                >
                  Save Draft
                </Button>
                <Button
                  size="sm"
                  onClick={handleSubmit}
                  disabled={timesheetStatus === "submitted" || timesheetStatus === "approved"}
                >
                  Submit for Approval
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {selectedTask && (
        <TaskPanel
          task={selectedTask}
          onClose={handleTaskPanelClose}
          onSave={handleTaskSave}
          onDelete={handleDeleteTask}
          isEditing={isEditingTask}
        />
      )}

      <SubmissionDialog
        open={showSubmissionDialog}
        onOpenChange={setShowSubmissionDialog}
        onConfirm={confirmSubmission}
        weekTotal={weekTotal}
        taskCount={tasks.length}
      />
    </div>
  )
}
