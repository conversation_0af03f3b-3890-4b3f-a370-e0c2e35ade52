"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Search, Edit, Trash2, Users, Clock, CheckCircle, RotateCcw } from "lucide-react"

interface Consultant {
  id: string
  firstName: string
  lastName: string
  email: string
  role: "consultant" | "admin"
  language: "en" | "fr"
  isActive: boolean
  totalHours: number
  timesheetsCount: number
  lastLogin: string
  createdAt: string
}

export function ConsultantManagement() {
  const [consultants, setConsultants] = useState<Consultant[]>([
    {
      id: "1",
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      role: "consultant",
      language: "en",
      isActive: true,
      totalHours: 520,
      timesheetsCount: 12,
      lastLogin: "2024-01-15",
      createdAt: "2023-08-15",
    },
    {
      id: "2",
      firstName: "Marie",
      lastName: "Dubois",
      email: "<EMAIL>",
      role: "consultant",
      language: "fr",
      isActive: true,
      totalHours: 680,
      timesheetsCount: 15,
      lastLogin: "2024-01-14",
      createdAt: "2023-09-20",
    },
    {
      id: "3",
      firstName: "Admin",
      lastName: "User",
      email: "<EMAIL>",
      role: "admin",
      language: "en",
      isActive: true,
      totalHours: 0,
      timesheetsCount: 0,
      lastLogin: "2024-01-16",
      createdAt: "2023-07-01",
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingConsultant, setEditingConsultant] = useState<Consultant | null>(null)
  const [newConsultant, setNewConsultant] = useState({
    firstName: "",
    lastName: "",
    email: "",
    role: "consultant" as const,
    language: "en" as const,
    isActive: true,
  })

  const filteredConsultants = consultants.filter(
    (consultant) =>
      consultant.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultant.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultant.email.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleCreateConsultant = () => {
    const consultant: Consultant = {
      id: Date.now().toString(),
      ...newConsultant,
      totalHours: 0,
      timesheetsCount: 0,
      lastLogin: "Never",
      createdAt: new Date().toISOString().split("T")[0],
    }
    setConsultants([...consultants, consultant])
    setNewConsultant({
      firstName: "",
      lastName: "",
      email: "",
      role: "consultant",
      language: "en",
      isActive: true,
    })
    setIsCreateDialogOpen(false)
  }

  const handleUpdateConsultant = () => {
    if (!editingConsultant) return
    setConsultants(consultants.map((c) => (c.id === editingConsultant.id ? editingConsultant : c)))
    setEditingConsultant(null)
  }

  const handleDeleteConsultant = (id: string) => {
    setConsultants(consultants.filter((c) => c.id !== id))
  }

  const toggleConsultantStatus = (id: string) => {
    setConsultants(consultants.map((c) => (c.id === id ? { ...c, isActive: !c.isActive } : c)))
  }

  const handleResetPassword = (id: string) => {
    // In a real app, this would trigger a password reset email
    alert("Password reset email sent!")
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Consultant Management</h1>
          <p className="text-gray-600">Manage consultant accounts and permissions</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-primary hover:bg-primary/90">
              <Plus className="h-4 w-4 mr-2" />
              Add Consultant
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Consultant</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    value={newConsultant.firstName}
                    onChange={(e) => setNewConsultant({ ...newConsultant, firstName: e.target.value })}
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    value={newConsultant.lastName}
                    onChange={(e) => setNewConsultant({ ...newConsultant, lastName: e.target.value })}
                    placeholder="Enter last name"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={newConsultant.email}
                  onChange={(e) => setNewConsultant({ ...newConsultant, email: e.target.value })}
                  placeholder="Enter email address"
                />
              </div>
              <div>
                <Label htmlFor="role">Role</Label>
                <Select
                  value={newConsultant.role}
                  onValueChange={(value: "consultant" | "admin") => setNewConsultant({ ...newConsultant, role: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="consultant">Consultant</SelectItem>
                    <SelectItem value="admin">Administrator</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="language">Language</Label>
                <Select
                  value={newConsultant.language}
                  onValueChange={(value: "en" | "fr") => setNewConsultant({ ...newConsultant, language: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="fr">Français</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={newConsultant.isActive}
                  onCheckedChange={(checked) => setNewConsultant({ ...newConsultant, isActive: checked })}
                />
                <Label htmlFor="active">Active Account</Label>
              </div>
              <Button onClick={handleCreateConsultant} className="w-full">
                Create Consultant
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Consultants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{consultants.length}</div>
            <p className="text-xs text-muted-foreground">{consultants.filter((c) => c.isActive).length} active</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Administrators</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{consultants.filter((c) => c.role === "admin").length}</div>
            <p className="text-xs text-muted-foreground">Admin accounts</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {consultants.reduce((sum, c) => sum + c.totalHours, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Logged by all consultants</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Timesheets</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{consultants.reduce((sum, c) => sum + c.timesheetsCount, 0)}</div>
            <p className="text-xs text-muted-foreground">Total submitted</p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search consultants..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Consultants Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Language</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Hours</TableHead>
              <TableHead>Timesheets</TableHead>
              <TableHead>Last Login</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredConsultants.map((consultant) => (
              <TableRow key={consultant.id}>
                <TableCell className="font-medium">
                  {consultant.firstName} {consultant.lastName}
                </TableCell>
                <TableCell>{consultant.email}</TableCell>
                <TableCell>
                  <Badge variant={consultant.role === "admin" ? "default" : "secondary"}>
                    {consultant.role === "admin" ? "Administrator" : "Consultant"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{consultant.language === "en" ? "English" : "Français"}</Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={consultant.isActive}
                      onCheckedChange={() => toggleConsultantStatus(consultant.id)}
                      size="sm"
                    />
                    <Badge variant={consultant.isActive ? "default" : "secondary"}>
                      {consultant.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell>{consultant.totalHours}h</TableCell>
                <TableCell>{consultant.timesheetsCount}</TableCell>
                <TableCell className="text-sm text-gray-600">{consultant.lastLogin}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" onClick={() => setEditingConsultant(consultant)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleResetPassword(consultant.id)}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteConsultant(consultant.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={!!editingConsultant} onOpenChange={() => setEditingConsultant(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Consultant</DialogTitle>
          </DialogHeader>
          {editingConsultant && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-firstName">First Name</Label>
                  <Input
                    id="edit-firstName"
                    value={editingConsultant.firstName}
                    onChange={(e) => setEditingConsultant({ ...editingConsultant, firstName: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-lastName">Last Name</Label>
                  <Input
                    id="edit-lastName"
                    value={editingConsultant.lastName}
                    onChange={(e) => setEditingConsultant({ ...editingConsultant, lastName: e.target.value })}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="edit-email">Email</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={editingConsultant.email}
                  onChange={(e) => setEditingConsultant({ ...editingConsultant, email: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-role">Role</Label>
                <Select
                  value={editingConsultant.role}
                  onValueChange={(value: "consultant" | "admin") =>
                    setEditingConsultant({ ...editingConsultant, role: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="consultant">Consultant</SelectItem>
                    <SelectItem value="admin">Administrator</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit-language">Language</Label>
                <Select
                  value={editingConsultant.language}
                  onValueChange={(value: "en" | "fr") =>
                    setEditingConsultant({ ...editingConsultant, language: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="fr">Français</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-active"
                  checked={editingConsultant.isActive}
                  onCheckedChange={(checked) => setEditingConsultant({ ...editingConsultant, isActive: checked })}
                />
                <Label htmlFor="edit-active">Active Account</Label>
              </div>
              <Button onClick={handleUpdateConsultant} className="w-full">
                Update Consultant
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
