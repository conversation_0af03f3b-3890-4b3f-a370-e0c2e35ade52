"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Users, Building2, LogOut, ChevronLeft, ChevronRight, Clock, CheckCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface SidebarProps {
  currentView: string
  onViewChange: (view: string) => void
  userRole: "consultant" | "admin"
}

export function Sidebar({ currentView, onViewChange, userRole }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)

  const consultantMenuItems = [
    { id: "timesheets", label: "My Timesheets", icon: Calendar },
    { id: "create", label: "Create Timesheet", icon: Clock },
  ]

  const adminMenuItems = [
    { id: "dashboard", label: "Timesheets", icon: CheckCircle },
    { id: "clients", label: "Manage Clients", icon: Building2 },
    { id: "consultants", label: "Manage Consultants", icon: Users },
    { id: "timesheets", label: "Manage Timesheets", icon: Calendar },
  ]

  const menuItems = userRole === "admin" ? adminMenuItems : consultantMenuItems

  return (
    <div
      className={cn(
        "bg-white border-r border-gray-200 flex flex-col transition-all duration-300",
        isCollapsed ? "w-16" : "w-64",
      )}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        {!isCollapsed && (
          <div>
            <h1 className="text-xl font-bold text-primary">TriTimesheet</h1>
            <p className="text-sm text-gray-600 capitalize">{userRole}</p>
          </div>
        )}
        <Button variant="ghost" size="sm" onClick={() => setIsCollapsed(!isCollapsed)} className="p-2">
          {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon
          return (
            <Button
              key={item.id}
              variant={currentView === item.id ? "default" : "ghost"}
              className={cn("w-full justify-start", isCollapsed && "px-2")}
              onClick={() => onViewChange(item.id)}
            >
              <Icon className="h-4 w-4" />
              {!isCollapsed && <span className="ml-2">{item.label}</span>}
            </Button>
          )
        })}
      </nav>

      {/* User Info & Logout */}
      <div className="p-4 border-t border-gray-200">
        {!isCollapsed && (
          <div className="mb-3">
            <p className="text-sm font-medium">John Doe</p>
            <p className="text-xs text-gray-600"><EMAIL></p>
            <Badge variant="secondary" className="mt-1 text-xs">
              {userRole === "admin" ? "Administrator" : "Consultant"}
            </Badge>
          </div>
        )}
        <Button variant="ghost" size="sm" className="w-full justify-start">
          <LogOut className="h-4 w-4" />
          {!isCollapsed && <span className="ml-2">Logout</span>}
        </Button>
      </div>
    </div>
  )
}
