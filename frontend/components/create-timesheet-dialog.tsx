"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent } from "@/components/ui/card"
import { CalendarDays, Clock, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface CreateTimesheetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateTimesheet: (weekStart: Date, saturdayEnabled: boolean) => void
  existingTimesheets: Record<string, string>
}

export function CreateTimesheetDialog({
  open,
  onOpenChange,
  onCreateTimesheet,
  existingTimesheets,
}: CreateTimesheetDialogProps) {
  const [selectedDate, setSelectedDate] = useState<Date>()
  const [saturdayEnabled, setSaturdayEnabled] = useState(false)

  const isWeekAvailable = (mondayDate: Date) => {
    const now = new Date()

    // Calculate the Friday of the selected week
    const friday = new Date(mondayDate)
    friday.setDate(mondayDate.getDate() + 4) // Monday + 4 days = Friday
    friday.setHours(12, 0, 0, 0) // Set to 12:00 PM

    // Week is only available if current time is past Friday 12pm of that week
    return now >= friday
  }

  const getTimesheetStatus = (date: Date) => {
    const dateString = date.toISOString().split("T")[0]
    return existingTimesheets[dateString]
  }

  const isMondayAvailable = (date: Date) => {
    const day = date.getDay()
    if (day !== 1) return false // Not a Monday

    if (!isWeekAvailable(date)) return false

    const status = getTimesheetStatus(date)
    return !status || status === "draft"
  }

  const getUnavailableReason = (date: Date) => {
    if (!isWeekAvailable(date)) {
      const friday = new Date(date)
      friday.setDate(date.getDate() + 4)
      return `Timesheets can only be created after Friday 12:00 PM (${friday.toLocaleDateString()})`
    }

    const status = getTimesheetStatus(date)
    if (status === "submitted") return "Timesheet already submitted for this week"
    if (status === "approved") return "Timesheet already approved for this week"
    if (status === "rejected") return "Timesheet exists but was rejected - contact admin"
    return null
  }

  const handleCreateTimesheet = () => {
    if (!selectedDate) return

    const status = getTimesheetStatus(selectedDate)
    if (status && status !== "draft") {
      console.log("[v0] Blocked creation - timesheet exists with status:", status)
      return
    }

    console.log("[v0] Creating timesheet for:", selectedDate, "Saturday enabled:", saturdayEnabled)
    onCreateTimesheet(selectedDate, saturdayEnabled)
    onOpenChange(false)

    setSelectedDate(undefined)
    setSaturdayEnabled(false)
  }

  const selectedDateReason = selectedDate ? getUnavailableReason(selectedDate) : null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CalendarDays className="h-5 w-5" />
            Create New Timesheet
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Date Selection */}
          <div className="space-y-3">
            <Label>Select Week Start (Monday)</Label>
            <Card>
              <CardContent className="p-3">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  disabled={(date) => !isMondayAvailable(date)}
                  className="rounded-md"
                />
              </CardContent>
            </Card>
            <p className="text-xs text-muted-foreground">
              Timesheets can only be created for past weeks (after Friday 12:00 PM). Grayed out dates are unavailable.
            </p>
          </div>

          {selectedDateReason && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{selectedDateReason}</AlertDescription>
            </Alert>
          )}

          {/* Saturday Toggle */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Enable Saturday</Label>
                <p className="text-sm text-muted-foreground">
                  Saturday is disabled by default but can be enabled for time entry
                </p>
              </div>
              <Switch checked={saturdayEnabled} onCheckedChange={setSaturdayEnabled} />
            </div>
          </div>

          {/* Preview */}
          {selectedDate && !selectedDateReason && (
            <Card className="bg-muted/30">
              <CardContent className="p-4">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Timesheet Preview
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Week Start:</span>
                    <span>
                      {selectedDate.toLocaleDateString("en-US", {
                        weekday: "long",
                        month: "long",
                        day: "numeric",
                        year: "numeric",
                      })}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Working Days:</span>
                    <span>Monday - {saturdayEnabled ? "Saturday" : "Friday"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Max Hours/Day:</span>
                    <span>8 hours</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="flex gap-3">
            <Button variant="outline" onClick={() => onOpenChange(false)} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleCreateTimesheet} disabled={!selectedDate || !!selectedDateReason} className="flex-1">
              Create Timesheet
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
