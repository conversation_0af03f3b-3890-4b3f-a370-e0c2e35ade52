"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Search, Edit, Trash2, Building2, Users, Clock } from "lucide-react"

interface Client {
  id: string
  name: string
  externalCode: string
  description: string
  isActive: boolean
  consultantCount: number
  totalHours: number
  createdAt: string
}

export function ClientManagement() {
  const [clients, setClients] = useState<Client[]>([
    {
      id: "1",
      name: "Acme Corporation",
      externalCode: "ACME001",
      description: "Large enterprise client for software development",
      isActive: true,
      consultantCount: 5,
      totalHours: 1240,
      createdAt: "2024-01-15",
    },
    {
      id: "2",
      name: "TechStart Inc",
      externalCode: "TECH002",
      description: "Startup consulting for digital transformation",
      isActive: true,
      consultantCount: 3,
      totalHours: 680,
      createdAt: "2024-02-20",
    },
    {
      id: "3",
      name: "Global Industries",
      externalCode: "GLOB003",
      description: "Manufacturing process optimization",
      isActive: false,
      consultantCount: 0,
      totalHours: 320,
      createdAt: "2023-11-10",
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | null>(null)
  const [newClient, setNewClient] = useState({
    name: "",
    externalCode: "",
    description: "",
    isActive: true,
  })

  const filteredClients = clients.filter(
    (client) =>
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.externalCode.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleCreateClient = () => {
    const client: Client = {
      id: Date.now().toString(),
      ...newClient,
      consultantCount: 0,
      totalHours: 0,
      createdAt: new Date().toISOString().split("T")[0],
    }
    setClients([...clients, client])
    setNewClient({ name: "", externalCode: "", description: "", isActive: true })
    setIsCreateDialogOpen(false)
  }

  const handleUpdateClient = () => {
    if (!editingClient) return
    setClients(clients.map((c) => (c.id === editingClient.id ? editingClient : c)))
    setEditingClient(null)
  }

  const handleDeleteClient = (id: string) => {
    setClients(clients.filter((c) => c.id !== id))
  }

  const toggleClientStatus = (id: string) => {
    setClients(clients.map((c) => (c.id === id ? { ...c, isActive: !c.isActive } : c)))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
          <p className="text-gray-600">Manage your consulting clients and projects</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-primary hover:bg-primary/90">
              <Plus className="h-4 w-4 mr-2" />
              Add Client
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Client</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Client Name</Label>
                <Input
                  id="name"
                  value={newClient.name}
                  onChange={(e) => setNewClient({ ...newClient, name: e.target.value })}
                  placeholder="Enter client name"
                />
              </div>
              <div>
                <Label htmlFor="code">External Code</Label>
                <Input
                  id="code"
                  value={newClient.externalCode}
                  onChange={(e) => setNewClient({ ...newClient, externalCode: e.target.value })}
                  placeholder="Enter external code"
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newClient.description}
                  onChange={(e) => setNewClient({ ...newClient, description: e.target.value })}
                  placeholder="Enter client description"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={newClient.isActive}
                  onCheckedChange={(checked) => setNewClient({ ...newClient, isActive: checked })}
                />
                <Label htmlFor="active">Active Client</Label>
              </div>
              <Button onClick={handleCreateClient} className="w-full">
                Create Client
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clients.length}</div>
            <p className="text-xs text-muted-foreground">{clients.filter((c) => c.isActive).length} active</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Consultants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clients.reduce((sum, c) => sum + c.consultantCount, 0)}</div>
            <p className="text-xs text-muted-foreground">Across all clients</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {clients.reduce((sum, c) => sum + c.totalHours, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Logged this year</p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search clients..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Clients Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Client Name</TableHead>
              <TableHead>External Code</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Consultants</TableHead>
              <TableHead>Total Hours</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredClients.map((client) => (
              <TableRow key={client.id}>
                <TableCell className="font-medium">{client.name}</TableCell>
                <TableCell>{client.externalCode}</TableCell>
                <TableCell className="max-w-xs truncate">{client.description}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Switch checked={client.isActive} onCheckedChange={() => toggleClientStatus(client.id)} size="sm" />
                    <Badge variant={client.isActive ? "default" : "secondary"}>
                      {client.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell>{client.consultantCount}</TableCell>
                <TableCell>{client.totalHours}h</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" onClick={() => setEditingClient(client)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteClient(client.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={!!editingClient} onOpenChange={() => setEditingClient(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Client</DialogTitle>
          </DialogHeader>
          {editingClient && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-name">Client Name</Label>
                <Input
                  id="edit-name"
                  value={editingClient.name}
                  onChange={(e) => setEditingClient({ ...editingClient, name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-code">External Code</Label>
                <Input
                  id="edit-code"
                  value={editingClient.externalCode}
                  onChange={(e) => setEditingClient({ ...editingClient, externalCode: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={editingClient.description}
                  onChange={(e) => setEditingClient({ ...editingClient, description: e.target.value })}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-active"
                  checked={editingClient.isActive}
                  onCheckedChange={(checked) => setEditingClient({ ...editingClient, isActive: checked })}
                />
                <Label htmlFor="edit-active">Active Client</Label>
              </div>
              <Button onClick={handleUpdateClient} className="w-full">
                Update Client
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
