"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, Plus, Filter, ChevronDown, CheckCircle, XCircle } from "lucide-react"
import { TimesheetGrid } from "./timesheet-grid"
import { CreateTimesheetDialog } from "./create-timesheet-dialog"

export function TimesheetDashboard() {
  const [view, setView] = useState<"grid" | "list">("list")
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [selectedTimesheet, setSelectedTimesheet] = useState<any>(null)

  const [timesheets, setTimesheets] = useState([
    {
      id: 1,
      weekStart: "2024-01-08",
      status: "draft",
      totalHours: 32.5,
      clients: ["Acme Corp", "TechStart"],
      lastModified: "2024-01-12",
    },
    {
      id: 2,
      weekStart: "2024-01-01",
      status: "submitted",
      totalHours: 40,
      clients: ["Acme Corp"],
      lastModified: "2024-01-05",
    },
    {
      id: 3,
      weekStart: "2023-12-25",
      status: "approved",
      totalHours: 35,
      clients: ["TechStart", "Global Inc"],
      lastModified: "2023-12-29",
    },
  ])

  const handleCreateTimesheet = (weekStart: Date, saturdayEnabled: boolean) => {
    const newTimesheet = {
      id: timesheets.length + 1,
      weekStart: weekStart.toISOString().split("T")[0],
      status: "draft",
      totalHours: 0,
      clients: [],
      lastModified: new Date().toISOString().split("T")[0],
      saturdayEnabled,
    }

    setTimesheets((prev) => [...prev, newTimesheet])
    setSelectedTimesheet(newTimesheet)
    setView("grid")
    console.log("[v0] Created new timesheet:", newTimesheet)
  }

  const getExistingTimesheets = () => {
    return timesheets.reduce(
      (acc, timesheet) => {
        acc[timesheet.weekStart] = timesheet.status
        return acc
      },
      {} as Record<string, string>,
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-muted text-muted-foreground"
      case "submitted":
        return "bg-accent text-accent-foreground"
      case "approved":
        return "bg-primary/10 text-primary"
      case "rejected":
        return "bg-destructive/10 text-destructive"
      default:
        return "bg-muted text-muted-foreground"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "submitted":
        return <Clock className="h-3 w-3" />
      case "approved":
        return <CheckCircle className="h-3 w-3" />
      case "rejected":
        return <XCircle className="h-3 w-3" />
      default:
        return null
    }
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-64 bg-sidebar border-r border-sidebar-border">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-sidebar-foreground">TriTimesheet</h1>
          <p className="text-sm text-sidebar-foreground/70 mt-1">Professional Time Tracking</p>
        </div>

        <nav className="px-4 space-y-2">
          <Button
            variant={view === "list" ? "secondary" : "ghost"}
            className="w-full justify-start"
            onClick={() => setView("list")}
          >
            <Clock className="mr-2 h-4 w-4" />
            My Timesheets
          </Button>
        </nav>

        {/* Quick Stats */}
        <div className="p-4 mt-8">
          <h3 className="text-sm font-medium text-sidebar-foreground mb-3">This Week</h3>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-sidebar-foreground/70">Hours Logged</span>
              <span className="font-medium text-sidebar-foreground">32.5h</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-sidebar-foreground/70">Tasks</span>
              <span className="font-medium text-sidebar-foreground">8</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-sidebar-foreground/70">Clients</span>
              <span className="font-medium text-sidebar-foreground">2</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-card border-b border-border px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-card-foreground">
                {view === "grid" ? "Week of January 8, 2024" : "My Timesheets"}
              </h2>
              <p className="text-sm text-muted-foreground mt-1">
                {view === "grid" ? "Drag and drop to manage your time slots" : "View and manage all your timesheets"}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                New Timesheet
              </Button>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <div className="flex-1 p-6">
          {view === "grid" ? (
            <TimesheetGrid />
          ) : (
            <div className="space-y-4">
              {timesheets.map((timesheet) => (
                <Card key={timesheet.id} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div>
                          <h3 className="font-medium text-card-foreground">
                            Week of{" "}
                            {new Date(timesheet.weekStart).toLocaleDateString("en-US", {
                              month: "long",
                              day: "numeric",
                              year: "numeric",
                            })}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {timesheet.clients.join(", ")} • {timesheet.totalHours}h total
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge className={`${getStatusColor(timesheet.status)} flex items-center gap-1`}>
                          {getStatusIcon(timesheet.status)}
                          {timesheet.status.charAt(0).toUpperCase() + timesheet.status.slice(1)}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedTimesheet(timesheet)
                            setView("grid")
                          }}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      <CreateTimesheetDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onCreateTimesheet={handleCreateTimesheet}
        existingTimesheets={getExistingTimesheets()}
      />
    </div>
  )
}
