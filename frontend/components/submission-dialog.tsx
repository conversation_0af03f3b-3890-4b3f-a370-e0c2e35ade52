"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Clock, Calendar, AlertTriangle } from "lucide-react"

interface SubmissionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void
  weekTotal: number
  taskCount: number
}

export function SubmissionDialog({ open, onOpenChange, onConfirm, weekTotal, taskCount }: SubmissionDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-accent" />
            Submit Timesheet for Approval
          </DialogTitle>
          <DialogDescription>
            Once submitted, you won't be able to edit this timesheet until it's approved or rejected.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-muted/30 p-4 rounded-lg space-y-3">
            <h4 className="font-medium text-sm">Timesheet Summary</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Total Hours</span>
                <Badge variant="outline">{weekTotal}h</Badge>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Tasks</span>
                <Badge variant="outline">{taskCount}</Badge>
              </div>
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            Your timesheet will be sent to administrators for review. You'll receive a notification once it's been
            processed.
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onConfirm}>Submit Timesheet</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
