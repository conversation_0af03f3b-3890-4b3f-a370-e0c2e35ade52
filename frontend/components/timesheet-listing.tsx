"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, Search, Filter, Eye, CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react"

interface Timesheet {
  id: string
  consultant: string
  client: string
  weekStart: string
  weekEnd: string
  totalHours: number
  status: "draft" | "submitted" | "approved" | "rejected"
  submittedDate?: string
  reviewedDate?: string
  reviewedBy?: string
}

// Mock data
const mockTimesheets: Timesheet[] = [
  {
    id: "TS001",
    consultant: "<PERSON>",
    client: "Acme Corp",
    weekStart: "2024-01-08",
    weekEnd: "2024-01-13",
    totalHours: 40,
    status: "approved",
    submittedDate: "2024-01-14",
    reviewedDate: "2024-01-15",
    reviewedBy: "Admin User",
  },
  {
    id: "TS002",
    consultant: "Jane <PERSON>",
    client: "TechStart Inc",
    weekStart: "2024-01-08",
    weekEnd: "2024-01-13",
    totalHours: 35,
    status: "submitted",
    submittedDate: "2024-01-14",
  },
  {
    id: "TS003",
    consultant: "Mike Johnson",
    client: "Global Solutions",
    weekStart: "2024-01-01",
    weekEnd: "2024-01-06",
    totalHours: 42,
    status: "rejected",
    submittedDate: "2024-01-07",
    reviewedDate: "2024-01-08",
    reviewedBy: "Admin User",
  },
  {
    id: "TS004",
    consultant: "Sarah Wilson",
    client: "Acme Corp",
    weekStart: "2024-01-15",
    weekEnd: "2024-01-20",
    totalHours: 38,
    status: "draft",
  },
]

const mockClients = ["All Clients", "Acme Corp", "TechStart Inc", "Global Solutions"]
const mockConsultants = ["All Consultants", "John Doe", "Jane Smith", "Mike Johnson", "Sarah Wilson"]

export function TimesheetListing() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedClient, setSelectedClient] = useState("All Clients")
  const [selectedConsultant, setSelectedConsultant] = useState("All Consultants")
  const [selectedStatus, setSelectedStatus] = useState("All Statuses")
  const [dateFilter, setDateFilter] = useState("")

  const filteredTimesheets = useMemo(() => {
    return mockTimesheets.filter((timesheet) => {
      const matchesSearch =
        timesheet.consultant.toLowerCase().includes(searchTerm.toLowerCase()) ||
        timesheet.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
        timesheet.id.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesClient = selectedClient === "All Clients" || timesheet.client === selectedClient
      const matchesConsultant = selectedConsultant === "All Consultants" || timesheet.consultant === selectedConsultant
      const matchesStatus = selectedStatus === "All Statuses" || timesheet.status === selectedStatus
      const matchesDate = !dateFilter || timesheet.weekStart.includes(dateFilter)

      return matchesSearch && matchesClient && matchesConsultant && matchesStatus && matchesDate
    })
  }, [searchTerm, selectedClient, selectedConsultant, selectedStatus, dateFilter])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "rejected":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "submitted":
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "approved":
        return "default"
      case "rejected":
        return "destructive"
      case "submitted":
        return "secondary"
      default:
        return "outline"
    }
  }

  const stats = useMemo(() => {
    const total = filteredTimesheets.length
    const approved = filteredTimesheets.filter((t) => t.status === "approved").length
    const pending = filteredTimesheets.filter((t) => t.status === "submitted").length
    const rejected = filteredTimesheets.filter((t) => t.status === "rejected").length
    const totalHours = filteredTimesheets.reduce((sum, t) => sum + t.totalHours, 0)

    return { total, approved, pending, rejected, totalHours }
  }, [filteredTimesheets])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">All Timesheets</h1>
          <p className="text-gray-600">Manage and review consultant timesheets</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Approved</p>
                <p className="text-xl font-bold text-green-600">{stats.approved}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Rejected</p>
                <p className="text-xl font-bold text-red-600">{stats.rejected}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-primary" />
              <div>
                <p className="text-sm text-gray-600">Total Hours</p>
                <p className="text-xl font-bold">{stats.totalHours}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-4 w-4" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search timesheets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedClient} onValueChange={setSelectedClient}>
              <SelectTrigger>
                <SelectValue placeholder="Select client" />
              </SelectTrigger>
              <SelectContent>
                {mockClients.map((client) => (
                  <SelectItem key={client} value={client}>
                    {client}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedConsultant} onValueChange={setSelectedConsultant}>
              <SelectTrigger>
                <SelectValue placeholder="Select consultant" />
              </SelectTrigger>
              <SelectContent>
                {mockConsultants.map((consultant) => (
                  <SelectItem key={consultant} value={consultant}>
                    {consultant}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All Statuses">All Statuses</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Input
              type="date"
              placeholder="Filter by date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
            />
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("")
                setSelectedClient("All Clients")
                setSelectedConsultant("All Consultants")
                setSelectedStatus("All Statuses")
                setDateFilter("")
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Timesheets Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Consultant</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Week Period</TableHead>
                <TableHead>Hours</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredTimesheets.map((timesheet) => (
                <TableRow key={timesheet.id}>
                  <TableCell className="font-medium">{timesheet.id}</TableCell>
                  <TableCell>{timesheet.consultant}</TableCell>
                  <TableCell>{timesheet.client}</TableCell>
                  <TableCell>
                    {new Date(timesheet.weekStart).toLocaleDateString()} -{" "}
                    {new Date(timesheet.weekEnd).toLocaleDateString()}
                  </TableCell>
                  <TableCell>{timesheet.totalHours}h</TableCell>
                  <TableCell>
                    <Badge variant={getStatusVariant(timesheet.status)} className="flex items-center space-x-1 w-fit">
                      {getStatusIcon(timesheet.status)}
                      <span className="capitalize">{timesheet.status}</span>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {timesheet.submittedDate ? new Date(timesheet.submittedDate).toLocaleDateString() : "-"}
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {filteredTimesheets.length === 0 && (
            <div className="text-center py-8 text-gray-500">No timesheets found matching your filters.</div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
