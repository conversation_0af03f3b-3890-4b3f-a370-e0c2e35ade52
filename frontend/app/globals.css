@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0); /* #ffffff - Clean white background */
  --foreground: oklch(0.35 0 0); /* #4b5563 - Professional dark gray text */
  --card: oklch(0.98 0 0); /* #f9fafb - Light gray card background */
  --card-foreground: oklch(0.35 0 0); /* #4b5563 - Dark gray text on cards */
  --popover: oklch(1 0 0); /* #ffffff - White popover background */
  --popover-foreground: oklch(0.35 0 0); /* #4b5563 - Dark gray popover text */
  --primary: oklch(0.55 0.15 200); /* #0891b2 - <PERSON>an primary color */
  --primary-foreground: oklch(1 0 0); /* #ffffff - White text on primary */
  --secondary: oklch(0.95 0.05 200); /* #a5f3fc - Light cyan accent */
  --secondary-foreground: oklch(0.35 0 0); /* #4b5563 - Dark gray on secondary */
  --muted: oklch(0.98 0 0); /* #f9fafb - Muted light gray */
  --muted-foreground: oklch(0.55 0 0); /* #9ca3af - Medium gray for muted text */
  --accent: oklch(0.95 0.05 200); /* #a5f3fc - Light cyan for highlights */
  --accent-foreground: oklch(0.35 0 0); /* #4b5563 - Dark gray on accent */
  --destructive: oklch(0.55 0.25 15); /* #be123c - Red for destructive actions */
  --destructive-foreground: oklch(1 0 0); /* #ffffff - White text on destructive */
  --border: oklch(0.92 0 0); /* #e5e7eb - Light gray borders */
  --input: oklch(1 0 0); /* #ffffff - White input background */
  --ring: oklch(0.55 0.15 200); /* #0891b2 - Cyan focus ring */
  --chart-1: oklch(0.95 0.05 200); /* #a5f3fc - Light cyan for charts */
  --chart-2: oklch(0.92 0.03 200); /* #e0f2fe - Very light cyan */
  --chart-3: oklch(0.88 0.08 200); /* #b1e5f2 - Medium light cyan */
  --chart-4: oklch(0.82 0.12 200); /* #81d4fa - Medium cyan */
  --chart-5: oklch(0.75 0.15 200); /* #4fc3f7 - Darker cyan */
  --radius: 0.5rem; /* 8px - Consistent border radius */
  --sidebar: oklch(0.98 0 0); /* #f9fafb - Light gray sidebar */
  --sidebar-foreground: oklch(0.35 0 0); /* #4b5563 - Dark gray sidebar text */
  --sidebar-primary: oklch(1 0 0); /* #ffffff - White sidebar primary */
  --sidebar-primary-foreground: oklch(0.35 0 0); /* #4b5563 - Dark gray sidebar primary text */
  --sidebar-accent: oklch(0.95 0.05 200); /* #a5f3fc - Light cyan sidebar accent */
  --sidebar-accent-foreground: oklch(0.35 0 0); /* #4b5563 - Dark gray sidebar accent text */
  --sidebar-border: oklch(0.92 0 0); /* #e5e7eb - Light gray sidebar border */
  --sidebar-ring: oklch(0.55 0.15 200); /* #0891b2 - Cyan sidebar focus ring */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --font-sans: "DM Sans", var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
