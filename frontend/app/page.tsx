"use client"

import { useState } from "react"
import { Sidebar } from "@/components/sidebar"
import { TimesheetDashboard } from "@/components/timesheet-dashboard"
import { ClientManagement } from "@/components/client-management"
import { ConsultantManagement } from "@/components/consultant-management"
import { TimesheetListing } from "@/components/timesheet-listing"

export default function Home() {
  const [currentView, setCurrentView] = useState("timesheets")
  const [userRole] = useState<"consultant" | "admin">("admin") // This would come from auth context

  const renderCurrentView = () => {
    switch (currentView) {
      case "clients":
        return <ClientManagement />
      case "consultants":
        return <ConsultantManagement />
      case "dashboard":
        return <TimesheetDashboard />
      case "timesheets":
        return userRole === "admin" ? <TimesheetListing /> : <TimesheetDashboard />
      default:
        return <TimesheetDashboard />
    }
  }

  return (
    <main className="min-h-screen bg-background flex">
      <Sidebar currentView={currentView} onViewChange={setCurrentView} userRole={userRole} />
      <div className="flex-1">{renderCurrentView()}</div>
    </main>
  )
}
