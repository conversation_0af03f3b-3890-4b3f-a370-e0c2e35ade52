import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"

export const metadata: Metadata = {
  title: "TriTimesheet - Professional Timesheet Management",
  description: "Modern timesheet management for consulting companies with drag & drop time entry",
  generator: "v0.app",
}

import ClientLayout from "./ClientLayout"

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return <ClientLayout>{children}</ClientLayout>
}
